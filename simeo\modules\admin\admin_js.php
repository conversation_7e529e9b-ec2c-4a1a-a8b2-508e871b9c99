<?php

function add_admin_js() {
    $screen = get_current_screen();
    
    // Vérifier si nous sommes sur la page principale du tableau de bord
    if ($screen->base == 'dashboard') {
        wp_enqueue_script('dashboard_js', get_template_directory_uri() . '/modules/admin/js/dashboard.js', array('jquery'), time(), true);
        wp_localize_script('dashboard_js', 'ajaxurl', admin_url('admin-ajax.php'));
    }

    // IF post type is clients, add the admin_js.js
    if (get_post_type() == 'clients') {
        wp_enqueue_script('clients_js', get_template_directory_uri() . '/modules/admin/js/clients.js', array('jquery'), time(), true);
        wp_localize_script('clients_js', 'ajaxurl', admin_url('admin-ajax.php'));
    }

    if (get_post_type() == 'orders') {
        wp_enqueue_script('order_js', get_template_directory_uri() . '/modules/admin/js/order.js', array('jquery'), time(), true);
        wp_localize_script('order_js', 'ajaxurl', admin_url('admin-ajax.php'));
    }

    if (get_post_type() == 'esims') {
        wp_enqueue_script('esim_js', get_template_directory_uri() . '/modules/admin/js/esims.js', array('jquery'), time(), true);
        wp_localize_script('esim_js', 'ajaxurl', admin_url('admin-ajax.php'));
    }

}
add_action('admin_enqueue_scripts', 'add_admin_js');

?>