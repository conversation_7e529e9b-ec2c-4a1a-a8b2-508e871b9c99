<?php

function create_post_type_clients() {
    register_post_type('clients',
        array(
            'labels'      => array(
                'name'          => __('Clients'),
                'singular_name' => __('Client')
            ),
            'public'      => true,
            'has_archive' => false,
            'rewrite'     => array('slug' => 'clients'),
            'show_in_rest' => false, // <PERSON><PERSON>
            'supports'    => array('title')
        )
    );

}
add_action('init', 'create_post_type_clients');

// Create metaboxs with eSIMs associated to the client
function create_metabox_esims_client() {
    add_meta_box('esims_client', 'eSIMs', 'esims_client_metabox', 'clients', 'normal', 'high');
}
add_action('add_meta_boxes', 'create_metabox_esims_client');
function esims_client_metabox($post) {}

// Create metaboxs with orders associated to the client
function create_metabox_orders_client() {
    add_meta_box('orders_client', 'Commandes', 'orders_client_metabox', 'clients', 'normal', 'high');
}
add_action('add_meta_boxes', 'create_metabox_orders_client');
function orders_client_metabox($post) {}

// Create metaboxs with inventory associated to the client
function create_metabox_inventory_client() {
    add_meta_box('inventory_client', 'Inventaire', 'inventory_client_metabox', 'clients', 'normal', 'high');
}
add_action('add_meta_boxes', 'create_metabox_inventory_client');
function inventory_client_metabox($post) {}

// On save client, update the credit in the database
function save_client($post_id) {
    if(get_post_type($post_id) == 'clients') {
        $conn = connect_to_datacenter();
        $credit = get_post_meta($post_id, 'credits', true);
        $query = "UPDATE users SET credit = $credit WHERE id = " . get_post_meta($post_id, 'client_id', true);
        $conn->query($query);
    }
}
add_action('save_post', 'save_client');
