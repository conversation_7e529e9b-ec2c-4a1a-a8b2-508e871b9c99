<?php

function retrieve_client_infos() {
    global $wpdb;

    $client_id = $_POST['client_id'];
    if(!is_numeric($client_id)) {
        echo 'Client ID is not a number';
        wp_die();
    }

    $conn = connect_to_datacenter();

    $query = "SELECT credit, stripe_customer_id, valid_account, active_zone, pass_token FROM users WHERE id = " . $_POST['client_id'];
    $result = $conn->query($query);
    $userInfo = array();
    if($result->num_rows > 0) {
        $row = $result->fetch_assoc();
        $row['credit'] = number_format($row['credit'], 2, '.', ',');
        $userInfo = $row;
    }else{
        echo 'Client not found';
        wp_die();
    }

    // Retrieve esims
    $query = "SELECT * FROM esims WHERE user_id = " . $_POST['client_id'] . " ORDER BY last_apply_date DESC";
    $result = $conn->query($query);
    $esims = array();
    if($result->num_rows > 0) {
        while($row = $result->fetch_assoc()) {
            $esims_post_id = post_exists($row['iccid']);
            $row['post_id'] = $esims_post_id;
            $esims[] = $row;
        }
    }

    // Retrieve orders
    $query = "SELECT id_order, product_name, total_paid, date FROM orders WHERE id_user = " . $_POST['client_id'];
    $result = $conn->query($query);
    $orders = array();
    if($result->num_rows > 0) {
        while($row = $result->fetch_assoc()) {
            $row['post_id'] = $wpdb->get_var("SELECT ID FROM wp_posts WHERE post_type = 'orders' AND post_title LIKE '%" . $row['id_order'] . "%'");
            $row['total_paid'] = number_format($row['total_paid'], 2, '.', ',') . ' $';
            $orders[] = $row;
        }
    }

    // Retrieve inventory
    $query = "SELECT * FROM inventories WHERE user_id = " . $_POST['client_id'];
    $result = $conn->query($query);
    $inventory = array();
    if($result->num_rows > 0) {
        while($row = $result->fetch_assoc()) {
            $inventory[] = $row;
        }
    }

    $return_array = array(
        'userInfo' => json_encode($userInfo),
        'esims' => json_encode($esims),
        'orders' => json_encode($orders),
        'inventory' => json_encode($inventory)
    );

    echo json_encode($return_array);
        
    wp_die();

}
add_action('wp_ajax_retrieve_client_infos', 'retrieve_client_infos');

?>