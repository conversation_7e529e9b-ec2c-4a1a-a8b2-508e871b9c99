<?php

$parse_uri = explode( 'wp-content', $_SERVER['SCRIPT_FILENAME'] );
require_once( $parse_uri[0] . 'wp-load.php' );

if ( ! is_admin() ) {
    require_once( ABSPATH . 'wp-admin/includes/post.php' );
}

//add debug
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);
date_default_timezone_set('America/Montreal');
$DATABASE = 'kfgkkncdzz';
$USERNAME = 'kfgkkncdzz';
$PASSWORD = 'WJS5PE9rZx';
$conn = new mysqli('localhost', $USERNAME, $PASSWORD, $DATABASE);

if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}else{
    echo "Connected successfully";
}

// get all users created in the last 7 days
$seven_days_ago = strtotime('-7 days');
$result = $conn->query("SELECT * FROM users WHERE created_at > $seven_days_ago");
while($row = $result->fetch_assoc()) {
    if(!post_exists($row['email'])){
        $wp_client_id = wp_insert_post(array(
            'post_type' => 'clients',
            'post_title' => $row['email'],
            'post_status' => 'publish',
            'post_author' => 1,
        ));
        
        update_post_meta($wp_client_id, 'client_id', $row['id']);
        update_post_meta($wp_client_id, 'courriel', $row['email']);
        update_post_meta($wp_client_id, 'date_dinscription', date('Ymd', $row['created_at']));
    }
}
