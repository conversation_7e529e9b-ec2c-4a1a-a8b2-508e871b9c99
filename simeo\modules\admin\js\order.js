console.log('order.js');

// Always remove parameter "refund=1" from the URL
jQuery(document).ready(function($) {
    setTimeout(function() {
        var url = new URL(window.location.href);
        url.searchParams.delete('refund');
        url.searchParams.delete('refundCredit');
        window.history.replaceState({}, document.title, url.toString());
    }, 1000);
});


// Display validation popup before refund with Yes/No
jQuery('.refund-stripe').click(function(event) {
    var confirm = window.confirm('Voulez-vous rembourser cette commande via Stripe ?');
    if(!confirm) {
        event.preventDefault();
    } else {
        window.location.href = jQuery(this).attr('href');
    }
});

jQuery('.refund-credit').click(function(event) {
    var confirm = window.confirm('Voulez-vous rembourser cette commande via crédit ?');
    if(!confirm) {
        event.preventDefault();
    } else {
        window.location.href = jQuery(this).attr('href');
    }
});

