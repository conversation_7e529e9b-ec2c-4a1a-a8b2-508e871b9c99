<?php

function my_metabox_action_client() {
    if(isset($_GET['post'])){
        $post = get_post($_GET['post']); 
        if ($post->post_type == 'clients') {
            add_meta_box( 'actions', 'Actions', 'action_button_client_meta_box', 'clients', 'normal' );
        }
    }
}
add_action( 'admin_init', 'my_metabox_action_client' );

function action_button_client_meta_box() {
    $post_id = $_GET['post']; 
    echo '<p><a href="/wp-admin/post.php?post=' . $post_id . '&action=edit&validAccount=1" class="btn-red-simeo valid-account">Valider le compte</a></p>';
}

// VALIDATE ACCOUNT
add_action('admin_init', 'validate_account');
function validate_account() {
    if(isset($_GET['validAccount']) && $_GET['validAccount'] == 1) {
        $post_id = $_GET['post'];
        $simeo_client_id = get_post_meta($post_id, 'client_id', true);
        $conn = connect_to_datacenter();
        $query = "UPDATE users SET valid_account = '1' WHERE id = '$simeo_client_id'";
        $conn->query($query);
        $conn->close();

        echo '<div class="notice notice-success is-dismissible"><p>Compte validé avec succès</p></div>';
    }
}

// REVOKE INVENTORY
add_action('admin_init', 'revoke_inventory');
function revoke_inventory() {
    if(isset($_GET['revokeInventory']) && $_GET['revokeInventory'] == 1) {

        if(is_numeric($_GET['inventoryId'])){
            $post_id = $_GET['post'];
            $inventory_id = $_GET['inventoryId'];
            $conn = connect_to_datacenter();
            $query = "DELETE FROM inventories WHERE id = '$inventory_id'";
            $conn->query($query);
            $conn->close();

            echo '<div class="notice notice-success is-dismissible"><p>Bundle révoqué de l\'inventaire avec succès</p></div>';
        }else{
            echo '<div class="notice notice-error is-dismissible"><p>Erreur lors de la révocation du bundle en inventaire ( introuvable )</p></div>';
        }
    }
}
?>