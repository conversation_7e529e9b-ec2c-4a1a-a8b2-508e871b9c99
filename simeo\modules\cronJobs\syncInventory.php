<?php
$parse_uri = explode( 'wp-content', $_SERVER['SCRIPT_FILENAME'] );
require_once( $parse_uri[0] . 'wp-load.php' );

// get inventory from esim-go
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'https://api.esim-go.com/v2.4/inventory');
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'X-API-Key: jdSohhA-m52upbK_pxL0Sa8yYIaSWAz6CaTjvG3X'
]);
curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "GET");
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 30);
$res_esimgo = curl_exec($ch);
curl_close($ch);


$inventoryListing = json_decode($res_esimgo);


$refund_inventory = [];
foreach($inventoryListing->bundles as $bundle){
    if(!is_array($bundle->available)){
        $bundle->available = [];
    }

    $refund_inventory[$bundle->name] = array("available" => count($bundle->available), "bundles" => $bundle->available);
}

echo "----------------------------------<br>";

$dataCenter = connect_to_datacenter();
$query = "SELECT COUNT(*) AS count, bundleName FROM inventories GROUP BY bundleName";
$result = $dataCenter->query($query);
while($row = $result->fetch_assoc()){
    //echo $row['bundleName'] . " : " . $row['count'] . "<br>";
    if(!isset($refund_inventory[$row['bundleName']])){
        echo "Bundle not found in esim-go inventory: " . $row['bundleName'] . "<br>";
    }else{
        $i = 0;
        while($i < $row['count']){
            array_pop($refund_inventory[$row['bundleName']]['bundles']);
            $i++;
        }
    }
}

echo "----------------------------------<br>";


foreach($refund_inventory as $bundleName => $bundleInfo){

    // esim-go refund to balance (POST CURL) WITH DATA usageId, quantity
    foreach($bundleInfo['bundles'] as $bundle){
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, 'https://api.esim-go.com/v2.4/inventory/refund');
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'X-API-Key: jdSohhA-m52upbK_pxL0Sa8yYIaSWAz6CaTjvG3X',
            'Content-Type: application/json'
        ]);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "POST");
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode([
            'usageId' => $bundle->id,
            'quantity' => 1
        ]));
        $res_esimgo = curl_exec($ch);
        curl_close($ch);
        $res_esimgo = json_decode($res_esimgo);
        
        echo "Refunded " . $bundle->id . " : " . $bundleName . "<br>";
    }
}