<?php

function add_search_dashboard_box() {
    add_meta_box(
        'search_dashboard', // ID of the meta box
        'Recherche', // Title of the meta box
        'search_dashboard_callback', // Callback function
        'dashboard', // Screen to display the meta box
        'side', // Context (normal, side, advanced)
        'high' // Priority (default, core, high, low)
    );
}

function search_dashboard_callback() {
    ob_start();
    ?>
    <form action="<?php echo admin_url('index.php'); ?>" method="get">
        <input type="text" id="search_dashboard_iccid" name="search_iccid" placeholder="Rechercher par ICCID" value="<?php echo isset($_GET['search_iccid']) ? $_GET['search_iccid'] : ''; ?>">
        <input type="text" id="search_dashboard_email" name="search_email" placeholder="Rechercher par courriel du client" value="<?php echo isset($_GET['search_email']) ? $_GET['search_email'] : ''; ?>">
        <input type="text" id="search_dashboard_order_id" name="search_order_id" placeholder="Rechercher par ID de commande" value="<?php echo isset($_GET['search_order_id']) ? $_GET['search_order_id'] : ''; ?>">
        <input type="button" id="search_dashboard_reset" name="search_dashboard_reset" value="Réinitialiser">
        <input type="submit" id="search_dashboard_submit" name="search_dashboard_submit" value="Rechercher">
    </form>
    <?php
    $content = ob_get_clean();
    echo $content;

    $is_iccid_search = isset($_GET['search_iccid']) && !empty($_GET['search_iccid']);
    $is_email_search = isset($_GET['search_email']) && !empty($_GET['search_email']);
    $is_order_id_search = isset($_GET['search_order_id']) && !empty($_GET['search_order_id']);
    if ($is_iccid_search || $is_email_search || $is_order_id_search) {
        add_meta_box(
            'search_dashboard_results', // ID of the meta box
            'Résultats de la recherche', // Title of the meta box
            'search_dashboard_results_callback', // Callback function
            'dashboard', // Screen to display the meta box
            'side', // Context (normal, side, advanced)
            'high' // Priority (default, core, high, low)
        );
    }
}



function search_dashboard_results_callback() {
    global $wpdb;

    // Search by ICCID
    if(isset($_GET['search_iccid']) && !empty($_GET['search_iccid'])) {

        $iccid = $_GET['search_iccid'];

        $eSIMS = $wpdb->get_results("SELECT * FROM wp_posts WHERE post_type = 'esims' AND post_title LIKE '%$iccid%'");
        if(empty($eSIMS)) {
            echo '<p>Aucun résultat trouvé</p>';
            return;
        }

        $eSIMS_obj = $eSIMS[0];
        $zone = get_post_meta($eSIMS_obj->ID, 'zone', true);
        $iso = get_iso_if_region($zone);

        // Get client id
        $simeo_client_id = get_post_meta($eSIMS_obj->ID, 'client_id', true);
        $client =  $wpdb->get_results("SELECT * FROM wp_posts p INNER JOIN wp_postmeta pm ON p.ID = pm.post_id WHERE p.post_type = 'clients' AND pm.meta_key = 'client_id' AND pm.meta_value = '$simeo_client_id'");
        $client_obj = $client[0];

        // Get order id
        $order =  $wpdb->get_results("SELECT * FROM wp_posts p INNER JOIN wp_postmeta pm_iduser ON p.ID = pm_iduser.post_id INNER JOIN wp_postmeta pm_zone ON p.ID = pm_zone.post_id WHERE p.post_type = 'orders' AND pm_iduser.meta_key = 'id_user' AND pm_iduser.meta_value = '$simeo_client_id' AND pm_zone.meta_key = 'produit_achete' AND pm_zone.meta_value LIKE '%_$iso_%'");
        echo '<h2 class="h2_search_dashboard">eSIM</h2>';
        echo '<table id="search_dashboard_results_table">';
        echo '<tr>';
        echo '<th>ICCID</th>';
        echo '<th>Zone</th>';
        echo '<th>Actions</th>';
        echo '</tr>';
        echo '<tr>';
        echo '<td>' . $eSIMS_obj->post_title . '</td>';
        echo '<td>' . $zone . '</td>';
        echo '<td><a href="/wp-admin/post.php?post=' . $eSIMS_obj->ID . '&action=edit">Voir l\'eSIM</a></td>';
        echo '</tr>';
        echo '</table>';

        echo '<h2 class="h2_search_dashboard">Client</h2>';
        echo '<table id="search_dashboard_results_table">';
        echo '<tr>';
        echo '<th>Courriel</th>';
        echo '<th>Actions</th>';
        echo '</tr>';
        echo '<tr>';
        echo '<td>' . $client_obj->post_title . '</td>';
        echo '<td><a href="/wp-admin/post.php?post=' . $client_obj->ID . '&action=edit">Voir la fiche client</a></td>';
        echo '</tr>';
        echo '</table>';

        echo '<h2 class="h2_search_dashboard">Commande du client au '.$zone.'</h2>';
        echo '<table id="search_dashboard_results_table">';
        echo '<tr>';
        echo '<th>Produit acheté</th>';
        echo '<th>Date de l\'achat</th>';
        echo '<th>Actions</th>';
        echo '</tr>';
        foreach($order as $order_obj) {
            $numero_de_transaction = get_post_meta($order_obj->ID, 'numero_de_transaction', true);
            $produit_achete = get_post_meta($order_obj->ID, 'produit_achete', true);
            $date_de_lachat = get_post_meta($order_obj->ID, 'date_de_lachat', true);

            echo '<tr>';
            echo '<td>' . $produit_achete . '</td>';
            echo '<td>' . $date_de_lachat . '</td>';
            echo '<td><a href="/wp-admin/post.php?post=' . $order_obj->ID . '&action=edit">Voir</a></td>';
            echo '</tr>';
        }


        echo '</table>';
    }

    if(isset($_GET['search_email']) && !empty($_GET['search_email'])) {
        $client = $wpdb->get_results("SELECT * FROM wp_posts p WHERE p.post_type = 'clients' AND p.post_title LIKE '%$_GET[search_email]%'");
        if(empty($client)) {
            echo '<p>Aucun résultat trouvé</p>';
            return;
        }

        if(count($client) > 1) {
            echo '<p>Plusieurs résultats trouvés</p>';
            foreach($client as $client_obj) {
                echo '<p><a href="/wp-admin/post.php?post=' . $client_obj->ID . '&action=edit">' . $client_obj->post_title . '</a></p>';
            }
            return;
        }else{
            $client_obj = $client[0];
            wp_redirect(admin_url('post.php?post=' . $client_obj->ID . '&action=edit'));
        }
    }

    if(isset($_GET['search_order_id']) && !empty($_GET['search_order_id'])) {
        $order = $wpdb->get_results("SELECT * FROM wp_posts p INNER JOIN wp_postmeta pm ON p.ID = pm.post_id WHERE p.post_type = 'orders' AND pm.meta_key = 'numero_de_transaction' AND pm.meta_value = '$_GET[search_order_id]'");
        if(empty($order)) {
            echo '<p>Aucun résultat trouvé</p>';
            return;
        }

        $order_obj = $order[0];
        wp_redirect(admin_url('post.php?post=' . $order_obj->ID . '&action=edit'));
    }
    


}

add_action('wp_dashboard_setup', 'add_search_dashboard_box');


?>