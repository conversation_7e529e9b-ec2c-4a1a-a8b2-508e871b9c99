<?php

function esim_meta_box(){
    global $wpdb;

    date_default_timezone_set('America/Montreal');
    $post_id = $_GET['post'];
    $post_title = get_the_title($post_id);

    $simeo_client_id = get_post_meta($post_id, 'client_id', true);
    $client_id = $wpdb->get_results("SELECT p.ID FROM wp_posts p INNER JOIN wp_postmeta pm ON p.ID = pm.post_id WHERE p.post_type = 'clients' AND pm.meta_key = 'client_id' AND pm.meta_value = '$simeo_client_id'");

    if(isset($client_id[0]->ID)){
        $user_id = $client_id[0]->ID;
    }else{
        $user_id = 0;
    }

    $activeEsim = json_decode(get_all_from_esim($post_title));
    $bundleContext = json_decode($activeEsim->bundleContext);
    $esim_info = json_decode($activeEsim->esim_info);
    if(!empty($activeEsim->esim_history)){
        $esim_history = json_decode($activeEsim->esim_history);
    }else{
        $esim_history = array();
    }
    $label_is_active = "<span style='color: green;'>Actif</span>";
    if(!isset($esim_info->firstInstalledDateTime)){
        $label_is_active = "<span style='color: red;'>eSIM non utilisable</span><br/>";
        //$label_is_active .= "<span style='font-size: 12px; color: red;'>Cette eSIM sera supprimée automatiquement dans quelques instants</span>";
        $label_is_active .= "<br/><a href='/wp-admin/post.php?post=" . $post_id . "&action=edit&deleteEsim=1&user_id=" . $user_id . "' class='btn-red-simeo delete-esim'>Supprimer l'eSIM</a>";

    }
    else if($esim_info->firstInstalledDateTime == "0"){
        $label_is_active = "<span style='color: red;'>Inactif</span>";
    }

    
    echo "<h3>eSIM ICCID: " . htmlspecialchars($activeEsim->iccID) . " - " . $label_is_active . "</h3>";

    if(isset($esim_info->firstInstalledDateTime)){
        echo "<table class='table table-striped'>";

        // Bundles
        echo "<tr><th>Description</th><th>Restant</th><th>Actions</th></tr>";
        foreach($bundleContext->bundles as $bundle){
            $data_restant = $bundle->assignments[0]->remainingQuantity / 1000000000;
            if($bundle->assignments[0]->bundleState == "queued"){
                echo "<tr><td>" . $bundle->description . " (" . htmlspecialchars($bundle->name)  . ")</td><td>Non commencé</td><td><a href='?post=" . $post_id . "&action=edit&revoke=1&assignmentId=" . $bundle->assignments[0]->id . "&bundleName=" . $bundle->name . "' class='button button-primary revoke-bundle'>Revoke</a></td></tr>";
            }else if($bundle->assignments[0]->bundleState == "active"){
                echo "<tr><td>" . $bundle->description . " (" . htmlspecialchars($bundle->name)  . ")</td><td>" . number_format($data_restant, 2) . " Go restant</td><td>N/D</td></tr>";
            }
        }
        echo "</table>";

        // Historique
        if(isset($esim_history->actions) && !empty($esim_history->actions)){
            echo "<h3>Historique</h3>";
            echo "<table class='table table-striped'>";
            echo "<tr><th>Date</th><th>Action</th><th>Forfait</th></tr>";
            foreach($esim_history->actions as $history){
                echo "<tr><td>" . date('d/m/Y H:i', strtotime($history->date)) . "</td><td>" . $history->name . "</td><td>" . $history->bundleName . "</td></tr>";
            }
            echo "</table>";
        }

        // Actions
        echo "<h3>Actions</h3>";
        echo "<div class='actions'>";
        if($user_id != 0){
            echo "<a href='/wp-admin/post.php?post=" . $user_id . "&action=edit' class='btn-red-simeo'>Accéder à la fiche utilisateur (incluant ses commandes)</a>";
        }else{
            echo "<span style='color: red;'>Aucun utilisateur trouvé</span>";
        }
        echo "<br/><br/><a href='/wp-admin/post.php?post=" . $post_id . "&action=edit&deleteEsim=1&user_id=" . $user_id . "' class='btn-red-simeo delete-esim'>Supprimer l'eSIM</a>";
        echo "</div>";


    }
}

function get_all_from_esim($iccID){
    $post_id = $_GET['post'];
    $user_id = get_post_meta($post_id, 'id_user', true);

    $conn = connect_to_datacenter();
    $query = "SELECT * FROM esims WHERE iccid = '$iccID'";
    $result = $conn->query($query);
    if($result->num_rows > 0){
        $iccID = $result->fetch_assoc()['iccid'];
        //var_dump($iccID);
        
    }else{
        //echo $query . "<br>";
        echo "Aucune carte trouvée";
        
    }
    $conn->close();

    #region cURL ESIM-GO - GET eSIM bundles
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'https://api.esim-go.com/v2.3/esims/' . $iccID . '/bundles');
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'X-API-Key: jdSohhA-m52upbK_pxL0Sa8yYIaSWAz6CaTjvG3X'
    ]);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
    curl_setopt($ch, CURLOPT_HEADER, 0);
    curl_setopt($ch, CURLOPT_NOBODY, 0);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    $res_esimgo = curl_exec($ch);
    curl_close($ch);

    #region cURL ESIM-GO - GET eSIM info
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'https://api.esim-go.com/v2.3/esims/' . $iccID);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'X-API-Key: jdSohhA-m52upbK_pxL0Sa8yYIaSWAz6CaTjvG3X'
    ]);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
    curl_setopt($ch, CURLOPT_HEADER, 0);
    curl_setopt($ch, CURLOPT_NOBODY, 0);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    $res_esim = curl_exec($ch);
    curl_close($ch);

    #region cURL ESIM-GO - GET eSIM history
    /*$ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'https://api.esim-go.com/v2.3/esims/' . $iccID . '/history');
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'X-API-Key: jdSohhA-m52upbK_pxL0Sa8yYIaSWAz6CaTjvG3X'
    ]);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
    curl_setopt($ch, CURLOPT_HEADER, 0);
    curl_setopt($ch, CURLOPT_NOBODY, 0);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    $res_esim_history = curl_exec($ch);
    curl_close($ch);
    #endregion
    */
    $res_esim_history = array();
    return json_encode(array('bundleContext' => $res_esimgo, 'iccID' => $iccID, 'esim_info' => $res_esim, 'esim_history' => $res_esim_history));
}

// REVOKE ESIMGO
add_action('admin_init', 'revoke_bundle');
function revoke_bundle() {
    if(isset($_GET['revoke']) && $_GET['revoke'] == 1 && isset($_GET['assignmentId']) && isset($_GET['bundleName'])) {
        $iccID = get_the_title($_GET['post']);
        global $message_revoke_bundle;
        
        // revoke bundle from eSIM to inventory
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, 'https://api.esim-go.com/v2.3/esims/' . $iccID . '/bundles/' . $_GET['bundleName'] . '/assignments/' . $_GET['assignmentId']);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'X-API-Key: jdSohhA-m52upbK_pxL0Sa8yYIaSWAz6CaTjvG3X'
        ]);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "DELETE");
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        $res_esimgo = curl_exec($ch);
        curl_close($ch);
        $esimgo_response = json_decode($res_esimgo);
        echo "<pre>";
        var_dump($esimgo_response->message);
        echo "</pre>";
        if(!isset($esimgo_response->message)){
            // add message in top of the page
            add_action('admin_notices', 'revoke_bundle_success_message');
            function revoke_bundle_success_message() {
                echo '<div class="notice notice-success is-dismissible"><p>Bundle révoqué avec succès</p></div>';
            }
        }else{
            $message_revoke_bundle = $esimgo_response->message;
            // add message in top of the page
            add_action('admin_notices', 'revoke_bundle_error_message');
            function revoke_bundle_error_message() {
                global $message_revoke_bundle;
                echo '<div class="notice notice-error is-dismissible"><p>Erreur lors de la révocation du bundle : ' . $message_revoke_bundle . '</p></div>';
            }
        }
    }
}
// END REVOKE ESIMGO

// DELETE ESIM
add_action('admin_init', 'delete_esim');
function delete_esim() {
    if(isset($_GET['deleteEsim']) && $_GET['deleteEsim'] == 1) {
        $iccID = get_the_title($_GET['post']);
        $conn = connect_to_datacenter();
        $query = "DELETE FROM esims WHERE iccid = '$iccID'";
        $conn->query($query);
        $conn->close();

        // delete post
        wp_delete_post($_GET['post']);

        wp_redirect('/wp-admin/post.php?post=' . $_GET['user_id'] . '&action=edit&deletedUserEsim=1');
        exit;
    }
}

add_action('admin_init', 'showMessageDeletedUserEsim');
function showMessageDeletedUserEsim() {
    if(isset($_GET['deletedUserEsim']) && $_GET['deletedUserEsim'] == 1) {
        echo '<div class="notice notice-success is-dismissible"><p>eSIM supprimée avec succès</p></div>';
    }
}
// END DELETE ESIM

?>