<?php

function create_post_type_bundle() {
    register_post_type('bundle',
        array(
            'labels'      => array(
                'name'          => __('Forfaits'),
                'singular_name' => __('Forfait')
            ),
            'public'      => true,
            'has_archive' => true,
            'rewrite'     => array('slug' => 'bundles'),
            'supports'    => array('title', 'editor', 'thumbnail')
        )
    );

    register_taxonomy('countries', 'bundle', array(
        'labels' => array(
            'name' => __('Pays'),
            'singular_name' => __('Pays')
        ),
        'hierarchical' => true,
        'public' => true,
        'show_ui' => true,
        'show_admin_column' => true,
        'query_var' => true,
        'rewrite' => array('slug' => 'countries'),
    ));
}
add_action('init', 'create_post_type_bundle');

